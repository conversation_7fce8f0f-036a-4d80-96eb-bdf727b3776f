//@version=6
indicator("Clean Trading Signals", shorttitle="CTS", overlay=true, max_bars_back=500)

// ═══════════════════════════════════════════════════════════════════════════════
// CLEAN SCALPING INDICATOR - SINGLE BEST SIGNALS ONLY
// ═══════════════════════════════════════════════════════════════════════════════

// Settings
ema_fast = input.int(3, "Fast EMA", minval=2, maxval=10)
ema_slow = input.int(8, "Slow EMA", minval=5, maxval=15)
rsi_period = input.int(5, "RSI Period", minval=2, maxval=10)
macd_fast = input.int(3, "MACD Fast", minval=2, maxval=8)
macd_slow = input.int(8, "MACD Slow", minval=5, maxval=15)
macd_signal = input.int(3, "MACD Signal", minval=2, maxval=8)
volume_period = input.int(5, "Volume Period", minval=3, maxval=10)
show_ema = input.bool(true, "Show EMA Lines")

// WIN RATE CALCULATION SETTINGS
enable_candle_selection = input.bool(false, "🎯 Enable Candle Selection Tool", tooltip="Check this to enable hover-based candle selection for win rate tracking")
selected_candle_index = input.int(0, "📍 Selected Candle (bars back)", minval=0, maxval=500, tooltip="Number of bars back from current bar to start tracking. 0=current bar, 10=10 bars ago")
reset_stats = input.bool(false, "🔄 Reset Statistics", tooltip="Check this to reset win rate and trade count")
show_selection_markers = input.bool(true, "📊 Show Selection Markers", tooltip="Show visual markers for selected candle")

// AUTOTRADING SETTINGS - STOCK MARKETS ONLY
enable_autotrading = input.bool(false, "🇮🇳 Enable Stock Autotrading", tooltip="Enable webhook alerts for automated stock trading")
trading_mode = input.string("Long Only", "📈 Trading Mode", options=["Long Only", "Long + Short"], tooltip="Long Only (Indian markets) or Long + Short (US markets)")
position_size = input.int(100, "💰 Position Size (Shares)", minval=1, maxval=10000, step=1, tooltip="Number of shares to trade per signal")
trading_symbol = input.string("RELIANCE", "📊 Stock Symbol", tooltip="Stock symbol (e.g., RELIANCE, TCS, INFY for NSE)")
exchange_code = input.string("NSE", "🏛️ Exchange", options=["NSE", "BSE", "NYSE", "NASDAQ", "LSE", "TSE"], tooltip="Stock exchange for trading")
use_stop_loss = input.bool(true, "🛡️ Use Stop Loss", tooltip="Include stop loss in autotrading")
use_take_profit = input.bool(true, "🎯 Use Take Profit", tooltip="Include take profit in autotrading")
broker_type = input.string("Angel One", "🏦 Broker", options=["Angel One", "Zerodha", "Upstox", "ICICI Direct", "HDFC Securities", "Kotak Securities", "Interactive Brokers", "TD Ameritrade", "E*TRADE"], tooltip="Your stock broker")

// ═══════════════════════════════════════════════════════════════════════════════
// PROFIT TRACKING VARIABLES - Declared early for use throughout script
// ═══════════════════════════════════════════════════════════════════════════════

// Performance tracking variables
var float total_profit = 0.0
var float total_profit_percent = 0.0
var int total_trades = 0
var int winning_trades = 0
var int losing_trades = 0
var float largest_win = 0.0
var float largest_loss = 0.0
var float win_rate = 0.0

// Track individual trade performance
var float trade_entry_price = na
var bool tracking_trade = false

// Candle selection tracking
var int selected_start_time = na
var bool selection_active = false

// ═══════════════════════════════════════════════════════════════════════════════
// TECHNICAL INDICATOR CALCULATIONS
// ═══════════════════════════════════════════════════════════════════════════════

// Moving Averages
ema_fast_line = ta.ema(close, ema_fast)
ema_slow_line = ta.ema(close, ema_slow)

// Trend Direction and Strength
trend_up = ema_fast_line > ema_slow_line
trend_strength_calc = math.abs(ema_fast_line - ema_slow_line) / close * 100
strong_trend = trend_strength_calc > 0.05

// RSI Momentum
rsi = ta.rsi(close, rsi_period)
rsi_bullish = rsi > 50 and rsi < 70
rsi_bearish = rsi < 50 and rsi > 30

// MACD Analysis
[macd_line, signal_line, histogram] = ta.macd(close, macd_fast, macd_slow, macd_signal)
macd_bullish = macd_line > signal_line and histogram > histogram[1]
macd_bearish = macd_line < signal_line and histogram < histogram[1]

// Volume Analysis
volume_ma = ta.sma(volume, volume_period)
volume_spike = volume > volume_ma * 1.2

// ATR for Volatility
atr = ta.atr(14)
atr_ma = ta.sma(atr, 14)
high_volatility = atr > atr_ma * 1.5  // Detect unusual volatility spikes

// TRAP AND CRASH DETECTION
// 1. Divergence Detection (Price vs RSI)
rsi_higher = rsi > rsi[1] and rsi[1] > rsi[2]
rsi_lower = rsi < rsi[1] and rsi[1] < rsi[2]
price_higher = close > close[1] and close[1] > close[2]
price_lower = close < close[1] and close[1] < close[2]

bearish_divergence = price_higher and rsi_lower  // Price up, RSI down = potential trap
bullish_divergence = price_lower and rsi_higher  // Price down, RSI up = potential reversal

// 2. Volume Divergence (Price vs Volume)
volume_declining = volume < ta.sma(volume, 5) * 0.8  // Weak volume
fake_breakout = (high > ta.highest(high[1], 10)) and volume_declining  // New high with weak volume

// 3. Sudden Price Drops Detection
price_drop_5min = (close - close[1]) / close[1] < -0.02  // 2% drop in 1 bar
price_drop_3bars = (close - close[3]) / close[3] < -0.05  // 5% drop in 3 bars
sudden_crash = price_drop_5min or price_drop_3bars

// 4. Overextended Conditions (Potential Reversal Zones)
rsi_overextended_bull = rsi > 80  // Extremely overbought
rsi_overextended_bear = rsi < 20  // Extremely oversold
price_vs_ema_distance = math.abs(close - ema_slow_line) / ema_slow_line * 100
overextended_from_ema = price_vs_ema_distance > 5  // More than 5% from EMA

// Market Structure (Higher Highs, Lower Lows) - INTRADAY OPTIMIZED
higher_high = high > ta.highest(high[1], 5)  // Shorter lookback for intraday
lower_low = low < ta.lowest(low[1], 5)  // Shorter lookback for intraday
market_structure_bullish = higher_high and not bearish_divergence and not fake_breakout
market_structure_bearish = lower_low and not bullish_divergence

// ═══════════════════════════════════════════════════════════════════════════════
// SIGNAL GENERATION LOGIC
// ═══════════════════════════════════════════════════════════════════════════════

// Primary Buy Conditions
buy_condition_1 = trend_up and strong_trend  // Strong uptrend
buy_condition_2 = rsi_bullish  // RSI in bullish zone
buy_condition_3 = macd_bullish  // MACD bullish crossover
buy_condition_4 = volume_spike  // Volume confirmation
buy_condition_5 = market_structure_bullish  // Market structure support

// Primary Sell Conditions
sell_condition_1 = not trend_up and strong_trend  // Strong downtrend
sell_condition_2 = rsi_bearish  // RSI in bearish zone
sell_condition_3 = macd_bearish  // MACD bearish crossover
sell_condition_4 = volume_spike  // Volume confirmation
sell_condition_5 = market_structure_bearish  // Market structure resistance

// Calculate recommendation scores for signal generation
bullish_score = (trend_up ? 1 : 0) + (strong_trend ? 1 : 0) + (rsi_bullish ? 1 : 0) + (macd_bullish ? 1 : 0) + (volume_spike ? 1 : 0)
bearish_score = (not trend_up ? 1 : 0) + (strong_trend ? 1 : 0) + (rsi_bearish ? 1 : 0) + (macd_bearish ? 1 : 0) + (volume_spike ? 1 : 0)

// Signal Filtering - Prevent too frequent signals
var int last_signal_bar = 0
bars_since_signal = bar_index - last_signal_bar
min_signal_gap = 2
sufficient_gap = bars_since_signal >= min_signal_gap

// TRAP AND CRASH FILTERS
avoid_buy_trap = bearish_divergence or fake_breakout or rsi_overextended_bull or overextended_from_ema
avoid_sell_trap = bullish_divergence or rsi_overextended_bear
crash_detected = sudden_crash or high_volatility

// AGGRESSIVE INTRADAY SIGNAL GENERATION - Quick scalping signals with trap protection
// Generate signal when we have good confirmation (3+ conditions) and no trap signals
buy_signal = bullish_score >= 3 and trend_up and sufficient_gap and not avoid_buy_trap and not crash_detected
sell_signal = bearish_score >= 3 and not trend_up and sufficient_gap and not avoid_sell_trap and not crash_detected

// Update last signal bar
if buy_signal or sell_signal
    last_signal_bar := bar_index

// ═══════════════════════════════════════════════════════════════════════════════
// RISK MANAGEMENT CALCULATIONS
// ═══════════════════════════════════════════════════════════════════════════════

// Stop Loss and Take Profit Levels
var float entry_price = na
var float stop_loss = na
var float take_profit = na
var bool in_long_position = false
var bool in_short_position = false
enable_sl_tp = true
sl_percentage = 2.0
tp_ratio = 2.0

// Position Management
if buy_signal and not in_long_position
    entry_price := close
    stop_loss := enable_sl_tp ? close * (1 - sl_percentage / 100) : na
    take_profit := enable_sl_tp ? close * (1 + (sl_percentage * tp_ratio) / 100) : na
    in_long_position := true
    in_short_position := false

if sell_signal and not in_short_position
    entry_price := close
    stop_loss := enable_sl_tp ? close * (1 + sl_percentage / 100) : na
    take_profit := enable_sl_tp ? close * (1 - (sl_percentage * tp_ratio) / 100) : na
    in_short_position := true
    in_long_position := false

// Exit Conditions
long_exit = in_long_position and (close <= stop_loss or close >= take_profit or sell_signal)
short_exit = in_short_position and (close >= stop_loss or close <= take_profit or buy_signal)

if long_exit
    in_long_position := false

if short_exit
    in_short_position := false

// ═══════════════════════════════════════════════════════════════════════════════
// VISUALIZATION AND PLOTTING
// ═══════════════════════════════════════════════════════════════════════════════

// EMA Lines
plot(show_ema ? ema_fast_line : na, "Fast EMA", color=color.blue, linewidth=2)
plot(show_ema ? ema_slow_line : na, "Slow EMA", color=color.red, linewidth=2)

// Clean chart - Only recommendation system (old buy/sell signals removed)

// Stop Loss and Take Profit Lines - Display only when close to current price
current_range = high - low
price_buffer = current_range * 3  // Only show SL/TP if within 3x current range

show_sl_tp = true
show_long_sl = show_sl_tp and in_long_position and not na(stop_loss) and math.abs(close - stop_loss) <= price_buffer
show_long_tp = show_sl_tp and in_long_position and not na(take_profit) and math.abs(close - take_profit) <= price_buffer
show_short_sl = show_sl_tp and in_short_position and not na(stop_loss) and math.abs(close - stop_loss) <= price_buffer
show_short_tp = show_sl_tp and in_short_position and not na(take_profit) and math.abs(close - take_profit) <= price_buffer

plot(show_long_sl ? stop_loss : na, "Long Stop Loss",
     color=color.new(color.red, 0), style=plot.style_linebr, linewidth=1)
plot(show_long_tp ? take_profit : na, "Long Take Profit",
     color=color.new(color.green, 0), style=plot.style_linebr, linewidth=1)
plot(show_short_sl ? stop_loss : na, "Short Stop Loss",
     color=color.new(color.red, 0), style=plot.style_linebr, linewidth=1)
plot(show_short_tp ? take_profit : na, "Short Take Profit",
     color=color.new(color.green, 0), style=plot.style_linebr, linewidth=1)

// Background Color for Trend
bgcolor(strong_trend and trend_up ? color.new(color.green, 95) : strong_trend and not trend_up ? color.new(color.red, 95) : na, title="Trend Background")

// ═══════════════════════════════════════════════════════════════════════════════
// BUY/SELL/HOLD RECOMMENDATION INDICATOR
// ═══════════════════════════════════════════════════════════════════════════════

// Use previously calculated scores from signal generation section

// Determine recommendation - AGGRESSIVE INTRADAY SCALPING
recommendation = bullish_score >= 3 and trend_up ? "BUY" : bearish_score >= 3 and not trend_up ? "SELL" : "HOLD"

// Recommendation colors
rec_color = recommendation == "BUY" ? color.new(color.green, 0) :
           recommendation == "SELL" ? color.new(color.red, 0) :
           color.new(color.gray, 0)

// Display recommendation as label on latest bar
if barstate.islast
    label.new(bar_index, high + (high - low) * 0.1,
              text=recommendation,
              style=label.style_label_down,
              color=rec_color,
              textcolor=color.white,
              size=size.large)

// Clean chart - no signal clutter

// Plot recommendation strength as histogram below chart
plot(bullish_score - bearish_score, "Recommendation Strength", color=bullish_score > bearish_score ? color.green : bearish_score > bullish_score ? color.red : color.gray, display=display.data_window)

// ═══════════════════════════════════════════════════════════════════════════════
// INFORMATION TABLE
// ═══════════════════════════════════════════════════════════════════════════════

// Create information table with profit tracking
var table info_table = table.new(position.top_right, 2, 12, bgcolor=color.white, border_width=1)

if barstate.islast
    table.cell(info_table, 0, 0, "Indicator", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 0, "Status", text_color=color.black, bgcolor=color.gray)

    table.cell(info_table, 0, 1, "Trend", text_color=color.black)
    table.cell(info_table, 1, 1, trend_up ? "BULLISH" : "BEARISH",
               text_color=trend_up ? color.green : color.red)

    table.cell(info_table, 0, 2, "Trend Strength", text_color=color.black)
    table.cell(info_table, 1, 2, strong_trend ? "STRONG" : "WEAK",
               text_color=strong_trend ? color.blue : color.gray)

    table.cell(info_table, 0, 3, "RSI", text_color=color.black)
    table.cell(info_table, 1, 3, str.tostring(math.round(rsi, 1)),
               text_color=rsi > 70 ? color.red : rsi < 30 ? color.green : color.black)

    table.cell(info_table, 0, 4, "MACD", text_color=color.black)
    table.cell(info_table, 1, 4, macd_bullish ? "BULLISH" : "BEARISH",
               text_color=macd_bullish ? color.green : color.red)

    table.cell(info_table, 0, 5, "Volume", text_color=color.black)
    table.cell(info_table, 1, 5, volume_spike ? "HIGH" : "NORMAL",
               text_color=volume_spike ? color.orange : color.black)

    table.cell(info_table, 0, 6, "Position", text_color=color.black)
    table.cell(info_table, 1, 6, in_long_position ? "LONG" : in_short_position ? "SHORT" : "NONE",
               text_color=in_long_position ? color.green : in_short_position ? color.red : color.black)

    table.cell(info_table, 0, 7, "ATR", text_color=color.black)
    table.cell(info_table, 1, 7, str.tostring(math.round(atr, 4)), text_color=color.black)

    // PROFIT TRACKING SECTION
    table.cell(info_table, 0, 8, "Total Profit", text_color=color.black, bgcolor=color.yellow)
    table.cell(info_table, 1, 8, str.tostring(math.round(total_profit, 2)),
               text_color=total_profit >= 0 ? color.green : color.red, bgcolor=color.yellow)

    table.cell(info_table, 0, 9, "Win Rate", text_color=color.black)
    table.cell(info_table, 1, 9, str.tostring(math.round(win_rate, 1)) + "%",
               text_color=win_rate >= 50 ? color.green : color.red)

    table.cell(info_table, 0, 10, "Total Trades", text_color=color.black)
    table.cell(info_table, 1, 10, str.tostring(total_trades), text_color=color.black)

    table.cell(info_table, 0, 11, "RECOMMENDATION", text_color=color.black, bgcolor=color.blue)
    table.cell(info_table, 1, 11, recommendation, text_color=color.white, bgcolor=rec_color)

// ═══════════════════════════════════════════════════════════════════════════════
// AUTOTRADING ALERT CONDITIONS
// ═══════════════════════════════════════════════════════════════════════════════

// Calculate stop loss and take profit levels for autotrading
buy_sl_price = use_stop_loss ? close * (1 - sl_percentage / 100) : 0
buy_tp_price = use_take_profit ? close * (1 + (sl_percentage * tp_ratio) / 100) : 0
sell_sl_price = use_stop_loss ? close * (1 + sl_percentage / 100) : 0
sell_tp_price = use_take_profit ? close * (1 - (sl_percentage * tp_ratio) / 100) : 0

// JSON formatted messages for stock trading bots
// Long Only Mode: BUY signal = BUY shares, SELL signal = SELL shares (exit position)
// Long + Short Mode: BUY signal = BUY shares, SELL signal = SHORT sell

long_only_mode = trading_mode == "Long Only"

// BUY signal always means BUY shares (open long position)
buy_message = '{"action": "BUY", "exchange": "' + exchange_code + '", "symbol": "' + trading_symbol + '", "quantity": ' + str.tostring(position_size) + ', "price": ' + str.tostring(close) + ', "order_type": "MARKET", "product": "MIS", "stop_loss": ' + str.tostring(buy_sl_price) + ', "target": ' + str.tostring(buy_tp_price) + ', "broker": "' + broker_type + '", "mode": "' + trading_mode + '", "timestamp": "' + str.tostring(time) + '"}'

// SELL signal behavior depends on trading mode
sell_message = long_only_mode ?
    '{"action": "SELL", "exchange": "' + exchange_code + '", "symbol": "' + trading_symbol + '", "quantity": ' + str.tostring(position_size) + ', "price": ' + str.tostring(close) + ', "order_type": "MARKET", "product": "MIS", "broker": "' + broker_type + '", "mode": "LONG_EXIT", "timestamp": "' + str.tostring(time) + '"}' :
    '{"action": "SELL_SHORT", "exchange": "' + exchange_code + '", "symbol": "' + trading_symbol + '", "quantity": ' + str.tostring(position_size) + ', "price": ' + str.tostring(close) + ', "order_type": "MARKET", "product": "MIS", "stop_loss": ' + str.tostring(sell_sl_price) + ', "target": ' + str.tostring(sell_tp_price) + ', "broker": "' + broker_type + '", "mode": "SHORT_ENTRY", "timestamp": "' + str.tostring(time) + '"}'

exit_long_message = '{"action": "SELL", "exchange": "' + exchange_code + '", "symbol": "' + trading_symbol + '", "quantity": ' + str.tostring(position_size) + ', "price": ' + str.tostring(close) + ', "order_type": "MARKET", "product": "MIS", "broker": "' + broker_type + '", "mode": "EXIT_LONG", "timestamp": "' + str.tostring(time) + '"}'

exit_short_message = '{"action": "BUY_TO_COVER", "exchange": "' + exchange_code + '", "symbol": "' + trading_symbol + '", "quantity": ' + str.tostring(position_size) + ', "price": ' + str.tostring(close) + ', "order_type": "MARKET", "product": "MIS", "broker": "' + broker_type + '", "mode": "EXIT_SHORT", "timestamp": "' + str.tostring(time) + '"}'

// Stock Autotrading Alert Conditions
if enable_autotrading
    alertcondition(buy_signal, title="🇮🇳 STOCK BUY ORDER", message=buy_message)
    alertcondition(sell_signal, title="🇮🇳 STOCK SELL ORDER", message=sell_message)
    alertcondition(long_exit, title="🇮🇳 STOCK EXIT LONG", message=exit_long_message)
    alertcondition(short_exit, title="🇮🇳 STOCK EXIT SHORT", message=exit_short_message)

// Manual Alert Conditions (when autotrading is disabled)
if not enable_autotrading
    alertcondition(buy_signal, title="📊 Manual Stock Buy", message="🚀 STOCK BUY SIGNAL! " + trading_symbol + " @ ₹" + str.tostring(close))
    alertcondition(sell_signal, title="📊 Manual Stock Sell", message="🔻 STOCK SELL SIGNAL! " + trading_symbol + " @ ₹" + str.tostring(close))
    alertcondition(long_exit or short_exit, title="📊 Manual Stock Exit", message="⚠️ STOCK EXIT! " + trading_symbol + " @ ₹" + str.tostring(close))

// ═══════════════════════════════════════════════════════════════════════════════
// PROFIT TRACKING SYSTEM - Shows actual performance if you followed signals
// ═══════════════════════════════════════════════════════════════════════════════

// SIMPLE CANDLE SELECTION SYSTEM
// Calculate selected candle time
if enable_candle_selection
    selected_start_time := time[selected_candle_index]
    selection_active := true
else
    // Default: track from when script was added (no past trades)
    if na(selected_start_time)
        selected_start_time := time
    selection_active := false

// Determine if we should track this trade
should_track = time >= selected_start_time and not reset_stats

// Visual marker for selected candle (must be at global scope)
show_selection_marker = enable_candle_selection and show_selection_markers and
                       bar_index == (bar_index - selected_candle_index)
plotshape(show_selection_marker, "Selected Candle", shape.triangleup, location.belowbar,
          color.new(color.yellow, 0), size=size.large, text="START")

// Show instruction when candle selection is enabled
if enable_candle_selection and show_selection_markers and barstate.islast
    label.new(bar_index - 10, high + (high - low) * 0.2,
              text="📍 TRACKING FROM " + str.tostring(selected_candle_index) + " BARS BACK",
              style=label.style_label_down,
              color=color.new(color.blue, 80),
              textcolor=color.white,
              size=size.small)

// Show default tracking status when candle selection is disabled
if not enable_candle_selection and show_selection_markers and barstate.islast
    label.new(bar_index - 10, high + (high - low) * 0.2,
              text="📊 AUTO TRACKING (NO PAST TRADES)",
              style=label.style_label_down,
              color=color.new(color.green, 80),
              textcolor=color.white,
              size=size.small)

// Reset statistics if requested
if reset_stats
    total_profit := 0.0
    total_profit_percent := 0.0
    total_trades := 0
    winning_trades := 0
    losing_trades := 0
    largest_win := 0.0
    largest_loss := 0.0

// Start tracking when signal occurs (only after selected start time)
if buy_signal and not tracking_trade and should_track
    trade_entry_price := close
    tracking_trade := true

if sell_signal and not tracking_trade and should_track
    trade_entry_price := close
    tracking_trade := true

// Calculate profit when position exits (only if tracking is active)
if tracking_trade and long_exit and should_track
    trade_profit = close - trade_entry_price
    trade_profit_percent = (trade_profit / trade_entry_price) * 100
    total_profit += trade_profit
    total_profit_percent += trade_profit_percent
    total_trades += 1

    if trade_profit > 0
        winning_trades += 1
        if trade_profit > largest_win
            largest_win := trade_profit
    else
        losing_trades += 1
        if trade_profit < largest_loss
            largest_loss := trade_profit

    tracking_trade := false

if tracking_trade and short_exit and should_track
    trade_profit = trade_entry_price - close
    trade_profit_percent = (trade_profit / trade_entry_price) * 100
    total_profit += trade_profit
    total_profit_percent += trade_profit_percent
    total_trades += 1

    if trade_profit > 0
        winning_trades += 1
        if trade_profit > largest_win
            largest_win := trade_profit
    else
        losing_trades += 1
        if trade_profit < largest_loss
            largest_loss := trade_profit

    tracking_trade := false

// Calculate win rate
win_rate := total_trades > 0 ? (winning_trades / total_trades) * 100 : 0

// ═══════════════════════════════════════════════════════════════════════════════
// STRATEGY DOCUMENTATION AND USAGE NOTES
// ═══════════════════════════════════════════════════════════════════════════════

// STRATEGY EXPLANATION:
// This indicator implements a comprehensive multi-signal trading strategy that combines:
// 1. TREND ANALYSIS: Uses dual EMA system to identify trend direction and strength
// 2. MOMENTUM: RSI and MACD for momentum confirmation
// 3. VOLUME: Volume spike analysis for signal validation
// 4. MARKET STRUCTURE: Higher highs/lower lows for additional confirmation
// 5. RISK MANAGEMENT: Automatic stop-loss and take-profit calculations
//
// OPTIMAL USAGE:
// - Best Timeframes: 15m, 1H, 4H, 1D
// - Market Conditions: Trending markets with moderate to high volatility
// - Asset Classes: Stocks, Forex, Crypto, Commodities
//
// SIGNAL INTERPRETATION:
// - BUY: All bullish conditions aligned (trend, momentum, volume, structure)
// - SELL: All bearish conditions aligned (trend, momentum, volume, structure)
// - EXIT: Stop-loss hit, take-profit reached, or opposite signal generated
//
// INTEGRATION WITH AUTOMATED SYSTEMS:
// - Use webhook alerts for real-time signal transmission
// - Implement position sizing based on ATR volatility
// - Consider market hours and session filters for specific markets
// - Backtest thoroughly before live implementation

// Ensure script is recognized as valid (TradingView requirement)
plot(na, title="Script Validator")
