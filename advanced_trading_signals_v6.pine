//@version=6
indicator("Advanced Multi-Signal Trading Strategy", shorttitle="AMTS", overlay=true, max_bars_back=500)

// ═══════════════════════════════════════════════════════════════════════════════
// STRATEGY OVERVIEW:
// This is a comprehensive trend-following strategy with momentum confirmation
// that combines multiple technical indicators for high-probability signals.
// 
// BEST TIMEFRAMES: 15m, 1H, 4H, 1D
// MARKET CONDITIONS: Works best in trending markets with moderate volatility
// STRATEGY TYPE: Hybrid (Trend Following + Momentum + Mean Reversion Filter)
// ═══════════════════════════════════════════════════════════════════════════════

// ═══════════════════════════════════════════════════════════════════════════════
// INPUT PARAMETERS - CUSTOMIZABLE SETTINGS
// ═══════════════════════════════════════════════════════════════════════════════

// Trend Analysis Settings - OPTIMIZED FOR SCALPING (ULTRA-FAST)
trend_group = "═══ TREND ANALYSIS ═══"
ema_fast = input.int(3, "Fast EMA Period", minval=2, maxval=10, group=trend_group)  // Ultra-fast response
ema_slow = input.int(8, "Slow EMA Period", minval=5, maxval=15, group=trend_group)  // Very quick trend detection
trend_strength = input.float(0.05, "Trend Strength Threshold (%)", minval=0.01, maxval=0.5, step=0.01, group=trend_group)  // Ultra-low threshold

// Momentum Settings - OPTIMIZED FOR SCALPING (ULTRA-SENSITIVE)
momentum_group = "═══ MOMENTUM ANALYSIS ═══"
rsi_period = input.int(5, "RSI Period", minval=2, maxval=10, group=momentum_group)  // Ultra-sensitive RSI
rsi_oversold = input.int(35, "RSI Oversold Level", minval=20, maxval=45, group=momentum_group)  // Wider range for quick signals
rsi_overbought = input.int(65, "RSI Overbought Level", minval=55, maxval=80, group=momentum_group)  // Wider range for quick signals
macd_fast = input.int(3, "MACD Fast Length", minval=2, maxval=8, group=momentum_group)  // Ultra-fast MACD
macd_slow = input.int(8, "MACD Slow Length", minval=5, maxval=15, group=momentum_group)  // Very quick signals
macd_signal = input.int(3, "MACD Signal Length", minval=2, maxval=8, group=momentum_group)  // Ultra-fast signal line

// Volume Analysis - OPTIMIZED FOR SCALPING
volume_group = "═══ VOLUME ANALYSIS ═══"
volume_ma_period = input.int(5, "Volume MA Period", minval=3, maxval=10, group=volume_group)  // Very short volume average
volume_threshold = input.float(1.1, "Volume Spike Threshold", minval=1.0, maxval=1.5, step=0.05, group=volume_group)  // Very low threshold

// Risk Management
risk_group = "═══ RISK MANAGEMENT ═══"
enable_sl_tp = input.bool(true, "Enable Stop Loss / Take Profit", group=risk_group)
sl_percentage = input.float(2.0, "Stop Loss (%)", minval=0.5, maxval=10.0, step=0.1, group=risk_group)
tp_ratio = input.float(2.0, "Take Profit Ratio (R:R)", minval=1.0, maxval=5.0, step=0.1, group=risk_group)

// Signal Filtering - OPTIMIZED FOR INTRADAY
filter_group = "═══ SIGNAL FILTERING ═══"
min_signal_gap = input.int(2, "Minimum Bars Between Signals", minval=1, maxval=10, group=filter_group)  // Allow more frequent signals
enable_market_structure = input.bool(true, "Enable Market Structure Filter", group=filter_group)
atr_period = input.int(10, "ATR Period (Volatility)", minval=3, maxval=21, group=filter_group)  // Shorter ATR

// Visual Settings
visual_group = "═══ VISUAL SETTINGS ═══"
show_ema = input.bool(true, "Show EMA Lines", group=visual_group)
show_sl_tp = input.bool(true, "Show Stop Loss / Take Profit", group=visual_group)

// ═══════════════════════════════════════════════════════════════════════════════
// PROFIT TRACKING VARIABLES - Declared early for use throughout script
// ═══════════════════════════════════════════════════════════════════════════════

// Performance tracking variables
var float total_profit = 0.0
var float total_profit_percent = 0.0
var int total_trades = 0
var int winning_trades = 0
var int losing_trades = 0
var float largest_win = 0.0
var float largest_loss = 0.0
var float win_rate = 0.0

// Track individual trade performance
var float trade_entry_price = na
var bool tracking_trade = false

// ═══════════════════════════════════════════════════════════════════════════════
// TECHNICAL INDICATOR CALCULATIONS
// ═══════════════════════════════════════════════════════════════════════════════

// Moving Averages
ema_fast_line = ta.ema(close, ema_fast)
ema_slow_line = ta.ema(close, ema_slow)

// Trend Direction and Strength
trend_up = ema_fast_line > ema_slow_line
trend_strength_calc = math.abs(ema_fast_line - ema_slow_line) / close * 100
strong_trend = trend_strength_calc > trend_strength

// RSI Momentum
rsi = ta.rsi(close, rsi_period)
rsi_bullish = rsi > 50 and rsi < rsi_overbought
rsi_bearish = rsi < 50 and rsi > rsi_oversold

// MACD Analysis
[macd_line, signal_line, histogram] = ta.macd(close, macd_fast, macd_slow, macd_signal)
macd_bullish = macd_line > signal_line and histogram > histogram[1]
macd_bearish = macd_line < signal_line and histogram < histogram[1]

// Volume Analysis
volume_ma = ta.sma(volume, volume_ma_period)
volume_spike = volume > volume_ma * volume_threshold

// ATR for Volatility
atr = ta.atr(atr_period)

// Market Structure (Higher Highs, Lower Lows) - INTRADAY OPTIMIZED
higher_high = high > ta.highest(high[1], 5)  // Shorter lookback for intraday
lower_low = low < ta.lowest(low[1], 5)  // Shorter lookback for intraday
market_structure_bullish = enable_market_structure ? higher_high : true
market_structure_bearish = enable_market_structure ? lower_low : true

// ═══════════════════════════════════════════════════════════════════════════════
// SIGNAL GENERATION LOGIC
// ═══════════════════════════════════════════════════════════════════════════════

// Primary Buy Conditions
buy_condition_1 = trend_up and strong_trend  // Strong uptrend
buy_condition_2 = rsi_bullish  // RSI in bullish zone
buy_condition_3 = macd_bullish  // MACD bullish crossover
buy_condition_4 = volume_spike  // Volume confirmation
buy_condition_5 = market_structure_bullish  // Market structure support

// Primary Sell Conditions
sell_condition_1 = not trend_up and strong_trend  // Strong downtrend
sell_condition_2 = rsi_bearish  // RSI in bearish zone
sell_condition_3 = macd_bearish  // MACD bearish crossover
sell_condition_4 = volume_spike  // Volume confirmation
sell_condition_5 = market_structure_bearish  // Market structure resistance

// Calculate recommendation scores for signal generation
bullish_score = (trend_up ? 1 : 0) + (strong_trend ? 1 : 0) + (rsi_bullish ? 1 : 0) + (macd_bullish ? 1 : 0) + (volume_spike ? 1 : 0)
bearish_score = (not trend_up ? 1 : 0) + (strong_trend ? 1 : 0) + (rsi_bearish ? 1 : 0) + (macd_bearish ? 1 : 0) + (volume_spike ? 1 : 0)

// Signal Filtering - Prevent too frequent signals
var int last_signal_bar = 0
bars_since_signal = bar_index - last_signal_bar
sufficient_gap = bars_since_signal >= min_signal_gap

// SINGLE BEST SIGNAL GENERATION - Only highest confidence signals
// Only generate signal when we have strong confirmation (4+ conditions)
buy_signal = bullish_score >= 4 and trend_up and sufficient_gap
sell_signal = bearish_score >= 4 and not trend_up and sufficient_gap

// Update last signal bar
if buy_signal or sell_signal
    last_signal_bar := bar_index

// ═══════════════════════════════════════════════════════════════════════════════
// RISK MANAGEMENT CALCULATIONS
// ═══════════════════════════════════════════════════════════════════════════════

// Stop Loss and Take Profit Levels
var float entry_price = na
var float stop_loss = na
var float take_profit = na
var bool in_long_position = false
var bool in_short_position = false

// Position Management
if buy_signal and not in_long_position
    entry_price := close
    stop_loss := enable_sl_tp ? close * (1 - sl_percentage / 100) : na
    take_profit := enable_sl_tp ? close * (1 + (sl_percentage * tp_ratio) / 100) : na
    in_long_position := true
    in_short_position := false

if sell_signal and not in_short_position
    entry_price := close
    stop_loss := enable_sl_tp ? close * (1 + sl_percentage / 100) : na
    take_profit := enable_sl_tp ? close * (1 - (sl_percentage * tp_ratio) / 100) : na
    in_short_position := true
    in_long_position := false

// Exit Conditions
long_exit = in_long_position and (close <= stop_loss or close >= take_profit or sell_signal)
short_exit = in_short_position and (close >= stop_loss or close <= take_profit or buy_signal)

if long_exit
    in_long_position := false
    
if short_exit
    in_short_position := false

// ═══════════════════════════════════════════════════════════════════════════════
// VISUALIZATION AND PLOTTING
// ═══════════════════════════════════════════════════════════════════════════════

// EMA Lines
plot(show_ema ? ema_fast_line : na, "Fast EMA", color=color.blue, linewidth=2)
plot(show_ema ? ema_slow_line : na, "Slow EMA", color=color.red, linewidth=2)

// Clean chart - Only recommendation system (old buy/sell signals removed)

// Stop Loss and Take Profit Lines - Display only when close to current price
current_range = high - low
price_buffer = current_range * 3  // Only show SL/TP if within 3x current range

show_long_sl = show_sl_tp and in_long_position and not na(stop_loss) and math.abs(close - stop_loss) <= price_buffer
show_long_tp = show_sl_tp and in_long_position and not na(take_profit) and math.abs(close - take_profit) <= price_buffer
show_short_sl = show_sl_tp and in_short_position and not na(stop_loss) and math.abs(close - stop_loss) <= price_buffer
show_short_tp = show_sl_tp and in_short_position and not na(take_profit) and math.abs(close - take_profit) <= price_buffer

plot(show_long_sl ? stop_loss : na, "Long Stop Loss",
     color=color.new(color.red, 0), style=plot.style_linebr, linewidth=1)
plot(show_long_tp ? take_profit : na, "Long Take Profit",
     color=color.new(color.green, 0), style=plot.style_linebr, linewidth=1)
plot(show_short_sl ? stop_loss : na, "Short Stop Loss",
     color=color.new(color.red, 0), style=plot.style_linebr, linewidth=1)
plot(show_short_tp ? take_profit : na, "Short Take Profit",
     color=color.new(color.green, 0), style=plot.style_linebr, linewidth=1)

// Background Color for Trend
bgcolor(strong_trend and trend_up ? color.new(color.green, 95) : strong_trend and not trend_up ? color.new(color.red, 95) : na, title="Trend Background")

// ═══════════════════════════════════════════════════════════════════════════════
// BUY/SELL/HOLD RECOMMENDATION INDICATOR
// ═══════════════════════════════════════════════════════════════════════════════

// Use previously calculated scores from signal generation section

// Determine recommendation - AGGRESSIVE FOR SCALPING
recommendation = bullish_score >= 3 ? "BUY" : bearish_score >= 3 ? "SELL" : bullish_score >= 2 and trend_up ? "WEAK BUY" : bearish_score >= 2 and not trend_up ? "WEAK SELL" : "HOLD"

// Recommendation colors
rec_color = recommendation == "BUY" ? color.new(color.green, 0) :
           recommendation == "WEAK BUY" ? color.new(color.green, 50) :
           recommendation == "SELL" ? color.new(color.red, 0) :
           recommendation == "WEAK SELL" ? color.new(color.red, 50) :
           color.new(color.gray, 0)

// Display recommendation as label on latest bar
if barstate.islast
    label.new(bar_index, high + (high - low) * 0.1,
              text=recommendation,
              style=label.style_label_down,
              color=rec_color,
              textcolor=color.white,
              size=size.large)

// Plot recommendation strength as histogram below chart
plot(bullish_score - bearish_score, "Recommendation Strength", color=bullish_score > bearish_score ? color.green : bearish_score > bullish_score ? color.red : color.gray, display=display.data_window)

// ═══════════════════════════════════════════════════════════════════════════════
// INFORMATION TABLE
// ═══════════════════════════════════════════════════════════════════════════════

// Create information table with profit tracking
var table info_table = table.new(position.top_right, 2, 12, bgcolor=color.white, border_width=1)

if barstate.islast
    table.cell(info_table, 0, 0, "Indicator", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 0, "Status", text_color=color.black, bgcolor=color.gray)

    table.cell(info_table, 0, 1, "Trend", text_color=color.black)
    table.cell(info_table, 1, 1, trend_up ? "BULLISH" : "BEARISH",
               text_color=trend_up ? color.green : color.red)

    table.cell(info_table, 0, 2, "Trend Strength", text_color=color.black)
    table.cell(info_table, 1, 2, strong_trend ? "STRONG" : "WEAK",
               text_color=strong_trend ? color.blue : color.gray)

    table.cell(info_table, 0, 3, "RSI", text_color=color.black)
    table.cell(info_table, 1, 3, str.tostring(math.round(rsi, 1)),
               text_color=rsi > 70 ? color.red : rsi < 30 ? color.green : color.black)

    table.cell(info_table, 0, 4, "MACD", text_color=color.black)
    table.cell(info_table, 1, 4, macd_bullish ? "BULLISH" : "BEARISH",
               text_color=macd_bullish ? color.green : color.red)

    table.cell(info_table, 0, 5, "Volume", text_color=color.black)
    table.cell(info_table, 1, 5, volume_spike ? "HIGH" : "NORMAL",
               text_color=volume_spike ? color.orange : color.black)

    table.cell(info_table, 0, 6, "Position", text_color=color.black)
    table.cell(info_table, 1, 6, in_long_position ? "LONG" : in_short_position ? "SHORT" : "NONE",
               text_color=in_long_position ? color.green : in_short_position ? color.red : color.black)

    table.cell(info_table, 0, 7, "ATR", text_color=color.black)
    table.cell(info_table, 1, 7, str.tostring(math.round(atr, 4)), text_color=color.black)

    // PROFIT TRACKING SECTION
    table.cell(info_table, 0, 8, "Total Profit", text_color=color.black, bgcolor=color.yellow)
    table.cell(info_table, 1, 8, str.tostring(math.round(total_profit, 2)),
               text_color=total_profit >= 0 ? color.green : color.red, bgcolor=color.yellow)

    table.cell(info_table, 0, 9, "Win Rate", text_color=color.black)
    table.cell(info_table, 1, 9, str.tostring(math.round(win_rate, 1)) + "%",
               text_color=win_rate >= 50 ? color.green : color.red)

    table.cell(info_table, 0, 10, "Total Trades", text_color=color.black)
    table.cell(info_table, 1, 10, str.tostring(total_trades), text_color=color.black)

    table.cell(info_table, 0, 11, "RECOMMENDATION", text_color=color.black, bgcolor=color.blue)
    table.cell(info_table, 1, 11, recommendation, text_color=color.white, bgcolor=rec_color)

// ═══════════════════════════════════════════════════════════════════════════════
// ALERT CONDITIONS
// ═══════════════════════════════════════════════════════════════════════════════

// Alert Conditions - Using simple constant messages
alertcondition(buy_signal, title="Buy Signal Alert", message="🚀 BUY SIGNAL GENERATED! Check chart for details.")
alertcondition(sell_signal, title="Sell Signal Alert", message="🔻 SELL SIGNAL GENERATED! Check chart for details.")
alertcondition(long_exit or short_exit, title="Position Exit Alert", message="⚠️ POSITION EXIT! Check chart for details.")

// ═══════════════════════════════════════════════════════════════════════════════
// PROFIT TRACKING SYSTEM - Shows actual performance if you followed signals
// ═══════════════════════════════════════════════════════════════════════════════

// Start tracking when signal occurs
if buy_signal and not tracking_trade
    trade_entry_price := close
    tracking_trade := true

if sell_signal and not tracking_trade
    trade_entry_price := close
    tracking_trade := true

// Calculate profit when position exits
if tracking_trade and long_exit
    trade_profit = close - trade_entry_price
    trade_profit_percent = (trade_profit / trade_entry_price) * 100
    total_profit += trade_profit
    total_profit_percent += trade_profit_percent
    total_trades += 1

    if trade_profit > 0
        winning_trades += 1
        if trade_profit > largest_win
            largest_win := trade_profit
    else
        losing_trades += 1
        if trade_profit < largest_loss
            largest_loss := trade_profit

    tracking_trade := false

if tracking_trade and short_exit
    trade_profit = trade_entry_price - close
    trade_profit_percent = (trade_profit / trade_entry_price) * 100
    total_profit += trade_profit
    total_profit_percent += trade_profit_percent
    total_trades += 1

    if trade_profit > 0
        winning_trades += 1
        if trade_profit > largest_win
            largest_win := trade_profit
    else
        losing_trades += 1
        if trade_profit < largest_loss
            largest_loss := trade_profit

    tracking_trade := false

// Calculate win rate
win_rate := total_trades > 0 ? (winning_trades / total_trades) * 100 : 0

// ═══════════════════════════════════════════════════════════════════════════════
// STRATEGY DOCUMENTATION AND USAGE NOTES
// ═══════════════════════════════════════════════════════════════════════════════

// STRATEGY EXPLANATION:
// This indicator implements a comprehensive multi-signal trading strategy that combines:
// 1. TREND ANALYSIS: Uses dual EMA system to identify trend direction and strength
// 2. MOMENTUM: RSI and MACD for momentum confirmation
// 3. VOLUME: Volume spike analysis for signal validation
// 4. MARKET STRUCTURE: Higher highs/lower lows for additional confirmation
// 5. RISK MANAGEMENT: Automatic stop-loss and take-profit calculations
//
// OPTIMAL USAGE:
// - Best Timeframes: 15m, 1H, 4H, 1D
// - Market Conditions: Trending markets with moderate to high volatility
// - Asset Classes: Stocks, Forex, Crypto, Commodities
//
// SIGNAL INTERPRETATION:
// - BUY: All bullish conditions aligned (trend, momentum, volume, structure)
// - SELL: All bearish conditions aligned (trend, momentum, volume, structure)
// - EXIT: Stop-loss hit, take-profit reached, or opposite signal generated
//
// INTEGRATION WITH AUTOMATED SYSTEMS:
// - Use webhook alerts for real-time signal transmission
// - Implement position sizing based on ATR volatility
// - Consider market hours and session filters for specific markets
// - Backtest thoroughly before live implementation

// Ensure script is recognized as valid (TradingView requirement)
plot(na, title="Script Validator")
